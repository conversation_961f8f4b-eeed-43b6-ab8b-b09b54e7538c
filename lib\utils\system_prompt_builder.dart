import '../models/models.dart';
import '../services/system_prompt_service.dart';

/// Utility class for building comprehensive system prompts that incorporate
/// UserProfile information and context for AI coaching conversations.
class SystemPromptBuilder {
  /// Builds a system prompt for chat with persona using configurable templates
  ///
  /// This method fetches the prompt template from SystemPromptService and injects
  /// the provided parameters (systemPersona, userProfile, conversationContext).
  ///
  /// Parameters are injected at entity level using placeholders:
  /// - {systemPersona} - Complete persona information
  /// - {userProfile} - Complete user profile information
  /// - {conversationContext} - Current conversation context
  static Future<String> buildChatWithPersonaSystemPrompt({
    SystemPersona? systemPersona,
    UserProfile? userProfile,
    String? conversationContext,
    UserSubscription? userSubscription,
  }) async {
    try {
      // Get the prompt template from SystemPromptService
      final template = await SystemPromptService.instance
          .getChatWithPersonaPrompt(userSubscription: userSubscription);

      // Inject parameters into the template
      String prompt = template;

      // Inject system persona information
      if (systemPersona != null) {
        final personaSection = _buildPersonaSection(systemPersona);
        prompt = prompt.replaceAll('{systemPersona}', personaSection);
      } else {
        prompt = prompt.replaceAll('{systemPersona}', '');
      }

      // Inject user profile information
      if (userProfile != null) {
        final profileSection = _buildUserProfileSection(userProfile);
        prompt = prompt.replaceAll('{userProfile}', profileSection);
      } else {
        prompt = prompt.replaceAll(
          '{userProfile}',
          'No user profile information available.',
        );
      }

      // Inject conversation context
      if (conversationContext != null && conversationContext.isNotEmpty) {
        final contextSection = _buildConversationContextSection(
          conversationContext,
        );
        prompt = prompt.replaceAll('{conversationContext}', contextSection);
      } else {
        final defaultContext = _buildDefaultContextSection();
        prompt = prompt.replaceAll('{conversationContext}', defaultContext);
      }

      return prompt.trim();
    } catch (e) {
      // Fallback to a basic prompt if template loading fails
      final buffer = StringBuffer();

      // Basic AI role section
      if (systemPersona != null) {
        buffer.writeln(_buildPersonaSection(systemPersona));
      } else {
        buffer.writeln('# AI Coach Role & Identity');
        buffer.writeln();
        buffer.writeln(
          'You are an AI coaching assistant designed to provide personalized guidance, support, and motivation.',
        );
      }

      buffer.writeln();
      buffer.writeln(_buildDefaultContextSection());

      if (userProfile != null) {
        buffer.writeln();
        buffer.writeln(_buildUserProfileSection(userProfile));
      }

      return buffer.toString().trim();
    }
  }

  /// Builds persona section for template injection
  static String _buildPersonaSection(SystemPersona systemPersona) {
    final buffer = StringBuffer();
    buffer.writeln('# AI Coach Role & Identity');
    buffer.writeln();

    buffer.writeln(
      'You are embodying the coaching persona: **${systemPersona.name}**',
    );

    if (systemPersona.description != null &&
        systemPersona.description!.isNotEmpty) {
      buffer.writeln();
      buffer.writeln('**Persona Description**: ${systemPersona.description}');
      buffer.writeln();
      buffer.writeln(
        'Embody this persona\'s unique coaching style and approach while maintaining these core characteristics:',
      );
    } else {
      buffer.writeln();
      buffer.writeln(
        'Embody this persona\'s unique coaching style and approach while maintaining these core characteristics:',
      );
    }

    buffer.writeln();
    buffer.writeln(
      '- **Empathetic & Understanding**: Listen actively and respond with genuine care',
    );
    buffer.writeln(
      '- **Adaptive Communication**: Adjust your style based on user preferences and personality',
    );
    buffer.writeln(
      '- **Goal-Oriented**: Help users identify, pursue, and achieve their objectives',
    );
    buffer.writeln(
      '- **Evidence-Based**: Provide practical, actionable advice grounded in coaching principles',
    );
    buffer.writeln(
      '- **Respectful Boundaries**: Maintain professional coaching relationships',
    );
    buffer.writeln(
      '- **Growth-Focused**: Encourage continuous learning and development',
    );
    buffer.writeln();
    buffer.writeln(
      'You are NOT a therapist or medical professional. For serious mental health or medical concerns, encourage users to seek appropriate professional help.',
    );

    return buffer.toString().trimRight();
  }

  /// Builds conversation context section for template injection
  static String _buildConversationContextSection(String context) {
    final now = DateTime.now();
    final timeZone = now.timeZoneName;
    final formattedTime = now.toIso8601String();
    final dayOfWeek = _getDayOfWeek(now.weekday);

    return '''# Current Context

- **Current Time**: $formattedTime ($timeZone)
- **Day**: $dayOfWeek
- **Session Type**: AI Coaching Conversation
- **Context**: $context''';
  }

  /// Builds default context section when no specific context is provided
  static String _buildDefaultContextSection() {
    final now = DateTime.now();
    final timeZone = now.timeZoneName;
    final formattedTime = now.toIso8601String();
    final dayOfWeek = _getDayOfWeek(now.weekday);

    return '''# Current Context

- **Current Time**: $formattedTime ($timeZone)
- **Day**: $dayOfWeek
- **Session Type**: AI Coaching Conversation''';
  }

  static String _buildUserProfileSection(UserProfile userProfile) {
    final buffer = StringBuffer();
    buffer.writeln('# User Profile');
    buffer.writeln();

    // Basic Information
    if (userProfile.name != null ||
        userProfile.age != null ||
        userProfile.gender != null) {
      buffer.writeln('## Basic Information');
      if (userProfile.name != null) {
        buffer.writeln('- **Name**: ${userProfile.name}');
      }
      if (userProfile.age != null) {
        buffer.writeln('- **Age**: ${userProfile.age}');
      }
      if (userProfile.gender != null) {
        buffer.writeln('- **Gender**: ${userProfile.gender}');
      }
      if (userProfile.familyStatus != null) {
        buffer.writeln('- **Family Status**: ${userProfile.familyStatus}');
      }
      if (userProfile.location != null) {
        final location = userProfile.location!;
        final locationStr = location.town != null
            ? '${location.town}, ${location.country}'
            : location.country;
        buffer.writeln('- **Location**: $locationStr');
      }
      buffer.writeln();
    }

    // Family & Relationships
    if (userProfile.family != null && userProfile.family!.isNotEmpty) {
      buffer.writeln('## Family & Relationships');
      for (final relation in userProfile.family!) {
        buffer.write(
          '- **${relation.name}** (${relation.relation}, age ${relation.age})',
        );
        if (relation.otherInfo != null && relation.otherInfo!.isNotEmpty) {
          buffer.write(' - ${relation.otherInfo!.join(', ')}');
        }
        buffer.writeln();
      }
      buffer.writeln();
    }

    // Goals
    if (userProfile.goals != null && userProfile.goals!.isNotEmpty) {
      buffer.writeln('## Current Goals');
      for (final goal in userProfile.goals!) {
        buffer.writeln('- **${goal.description}** (Status: ${goal.status})');
      }
      buffer.writeln();
    }

    // Preferences & Interests
    if (userProfile.likes != null && userProfile.likes!.isNotEmpty) {
      buffer.writeln('## Interests & Likes');
      buffer.writeln('- ${userProfile.likes!.join(', ')}');
      buffer.writeln();
    }

    if (userProfile.dislikes != null && userProfile.dislikes!.isNotEmpty) {
      buffer.writeln('## Dislikes');
      buffer.writeln('- ${userProfile.dislikes!.join(', ')}');
      buffer.writeln();
    }

    // Personality Traits
    if (userProfile.personalityTraits != null &&
        userProfile.personalityTraits!.isNotEmpty) {
      buffer.writeln('## Personality Traits');
      buffer.writeln('- ${userProfile.personalityTraits!.join(', ')}');
      buffer.writeln();
    }

    // Key Facts
    if (userProfile.facts != null && userProfile.facts!.isNotEmpty) {
      buffer.writeln('## Important Facts');
      for (final fact in userProfile.facts!) {
        buffer.writeln('- **${fact.key}**: ${_formatFactValue(fact.value)}');
      }
      buffer.writeln();
    }

    // Preferences
    if (userProfile.preferences != null &&
        userProfile.preferences!.isNotEmpty) {
      buffer.writeln('## Communication Preferences');
      userProfile.preferences!.forEach((key, value) {
        buffer.writeln('- **$key**: $value');
      });
      buffer.writeln();
    }

    return buffer.toString().trimRight();
  }

  /// Safely converts a fact value (dynamic type) to a String for display
  /// Handles null, String, int, bool, and other types appropriately
  static String _formatFactValue(dynamic value) {
    if (value == null) {
      return 'Not set';
    }

    if (value is String) {
      return value.isEmpty ? 'Empty' : value;
    }

    if (value is int || value is double) {
      return value.toString();
    }

    if (value is bool) {
      return value ? 'Yes' : 'No';
    }

    // For any other type, convert to string
    return value.toString();
  }

  static String _getDayOfWeek(int weekday) {
    const days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ];
    return days[weekday - 1];
  }
}
