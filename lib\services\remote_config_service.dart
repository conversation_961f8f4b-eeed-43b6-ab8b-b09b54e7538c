import 'package:firebase_remote_config/firebase_remote_config.dart';
import '../models/models.dart';
import 'logging_service.dart';
import 'subscription_service.dart';

/// Service for managing Firebase Remote Config for AI model configuration
///
/// This service handles:
/// - Firebase Remote Config initialization and configuration fetching
/// - AI model selection based on use case context and user subscription tier
/// - Fallback to default models when remote config is unavailable
/// - Error handling and logging for configuration operations
class RemoteConfigService {
  static const String _logTag = 'RemoteConfigService';
  static RemoteConfigService? _instance;

  bool _isInitialized = false;
  FirebaseRemoteConfig? _remoteConfig;

  // Remote config keys for AI model configuration
  static const String _chatModelKey = 'gemini_chat_model';
  static const String _titleGenerationModelKey =
      'gemini_title_generation_model';
  static const String _profileUpdateModelKey = 'gemini_profile_update_model';
  static const String _premiumChatModelKey = 'gemini_premium_chat_model';
  static const String _premiumTitleGenerationModelKey =
      'gemini_premium_title_generation_model';
  static const String _premiumProfileUpdateModelKey =
      'gemini_premium_profile_update_model';

  // Remote config keys for system prompts
  static const String _systemPromptChatWithPersonaKey =
      'system_prompt_chat_with_persona';
  static const String _systemPromptTitleGenerationKey =
      'system_prompt_title_generation';
  static const String _systemPromptProfileUpdateKey =
      'system_prompt_profile_update';

  // Default fallback models
  static const String _defaultModel = 'gemini-2.5-flash';
  static const String _defaultPremiumModel = 'gemini-2.5-flash';

  /// Private constructor for singleton pattern
  RemoteConfigService._();

  /// Get the singleton instance
  static RemoteConfigService get instance {
    _instance ??= RemoteConfigService._();
    return _instance!;
  }

  /// Initialize the Remote Config service
  /// Must be called before using the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Initializing RemoteConfigService',
      );

      _remoteConfig = FirebaseRemoteConfig.instance;

      // Set configuration settings
      await _remoteConfig!.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(hours: 1),
        ),
      );

      // Set default values
      await _remoteConfig!.setDefaults(_getDefaultValues());

      // Fetch and activate remote config
      await _fetchAndActivate();

      _isInitialized = true;

      LoggingService.instance.logInfo(
        _logTag,
        'RemoteConfigService initialized successfully',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to initialize RemoteConfigService',
      );
      // Don't rethrow - allow app to continue with default values
    }
  }

  /// Fetch and activate remote configuration
  Future<void> _fetchAndActivate() async {
    try {
      final bool updated = await _remoteConfig!.fetchAndActivate();

      LoggingService.instance.logInfo(
        _logTag,
        'Remote config fetch completed. Updated: $updated',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to fetch and activate remote config',
      );
      // Continue with cached/default values
    }
  }

  /// Get default configuration values
  Map<String, dynamic> _getDefaultValues() {
    return {
      // AI model defaults
      _chatModelKey: _defaultModel,
      _titleGenerationModelKey: _defaultModel,
      _profileUpdateModelKey: _defaultModel,
      _premiumChatModelKey: _defaultPremiumModel,
      _premiumTitleGenerationModelKey: _defaultPremiumModel,
      _premiumProfileUpdateModelKey: _defaultPremiumModel,

      // System prompt defaults (empty strings - will fallback to local assets)
      _systemPromptChatWithPersonaKey: '',
      _systemPromptTitleGenerationKey: '',
      _systemPromptProfileUpdateKey: '',
    };
  }

  /// Ensure the service is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError(
        'RemoteConfigService must be initialized before use. Call initialize() first.',
      );
    }
  }

  /// Get the appropriate Gemini model for chat conversations
  String getChatModel({UserSubscription? userSubscription}) {
    try {
      _ensureInitialized();

      final bool isPremium = userSubscription?.hasPremiumAccess ?? false;
      final String configKey = isPremium ? _premiumChatModelKey : _chatModelKey;
      final String defaultValue = _getDefaultValues()[configKey] as String;

      final String model = _remoteConfig?.getString(configKey) ?? defaultValue;

      LoggingService.instance.logInfo(
        _logTag,
        'Selected chat model: $model (premium: $isPremium)',
      );

      return model;
    } catch (e) {
      final bool isPremium = userSubscription?.hasPremiumAccess ?? false;
      final String configKey = isPremium ? _premiumChatModelKey : _chatModelKey;
      final String defaultValue = _getDefaultValues()[configKey] as String;

      LoggingService.instance.logInfo(
        _logTag,
        'Error getting chat model, using default: $e',
      );
      return defaultValue;
    }
  }

  /// Get the appropriate Gemini model for title generation
  String getTitleGenerationModel({UserSubscription? userSubscription}) {
    try {
      _ensureInitialized();

      final bool isPremium = userSubscription?.hasPremiumAccess ?? false;
      final String configKey = isPremium
          ? _premiumTitleGenerationModelKey
          : _titleGenerationModelKey;
      final String defaultValue = _getDefaultValues()[configKey] as String;

      final String model = _remoteConfig?.getString(configKey) ?? defaultValue;

      LoggingService.instance.logInfo(
        _logTag,
        'Selected title generation model: $model (premium: $isPremium)',
      );

      return model;
    } catch (e) {
      final bool isPremium = userSubscription?.hasPremiumAccess ?? false;
      final String configKey = isPremium
          ? _premiumTitleGenerationModelKey
          : _titleGenerationModelKey;
      final String defaultValue = _getDefaultValues()[configKey] as String;

      LoggingService.instance.logInfo(
        _logTag,
        'Error getting title generation model, using default: $e',
      );
      return defaultValue;
    }
  }

  /// Get the appropriate Gemini model for profile updates
  String getProfileUpdateModel({UserSubscription? userSubscription}) {
    try {
      _ensureInitialized();

      final bool isPremium = userSubscription?.hasPremiumAccess ?? false;
      final String configKey = isPremium
          ? _premiumProfileUpdateModelKey
          : _profileUpdateModelKey;
      final String defaultValue = _getDefaultValues()[configKey] as String;

      final String model = _remoteConfig?.getString(configKey) ?? defaultValue;

      LoggingService.instance.logInfo(
        _logTag,
        'Selected profile update model: $model (premium: $isPremium)',
      );

      return model;
    } catch (e) {
      final bool isPremium = userSubscription?.hasPremiumAccess ?? false;
      final String configKey = isPremium
          ? _premiumProfileUpdateModelKey
          : _profileUpdateModelKey;
      final String defaultValue = _getDefaultValues()[configKey] as String;

      LoggingService.instance.logInfo(
        _logTag,
        'Error getting profile update model, using default: $e',
      );
      return defaultValue;
    }
  }

  /// Manually refresh remote configuration
  /// Useful for testing or when immediate updates are needed
  Future<void> refresh() async {
    try {
      _ensureInitialized();
      await _fetchAndActivate();

      LoggingService.instance.logInfo(
        _logTag,
        'Remote config refreshed successfully',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to refresh remote config',
      );
    }
  }

  /// Get all current configuration values for debugging
  Map<String, String> getAllConfigValues() {
    try {
      _ensureInitialized();

      return {
        // AI model configurations
        _chatModelKey: _remoteConfig?.getString(_chatModelKey) ?? _defaultModel,
        _titleGenerationModelKey:
            _remoteConfig?.getString(_titleGenerationModelKey) ?? _defaultModel,
        _profileUpdateModelKey:
            _remoteConfig?.getString(_profileUpdateModelKey) ?? _defaultModel,
        _premiumChatModelKey:
            _remoteConfig?.getString(_premiumChatModelKey) ??
            _defaultPremiumModel,
        _premiumTitleGenerationModelKey:
            _remoteConfig?.getString(_premiumTitleGenerationModelKey) ??
            _defaultPremiumModel,
        _premiumProfileUpdateModelKey:
            _remoteConfig?.getString(_premiumProfileUpdateModelKey) ??
            _defaultPremiumModel,

        // System prompt configurations
        _systemPromptChatWithPersonaKey:
            _remoteConfig?.getString(_systemPromptChatWithPersonaKey) ?? '',
        _systemPromptTitleGenerationKey:
            _remoteConfig?.getString(_systemPromptTitleGenerationKey) ?? '',
        _systemPromptProfileUpdateKey:
            _remoteConfig?.getString(_systemPromptProfileUpdateKey) ?? '',
      };
    } catch (e) {
      LoggingService.instance.logInfo(
        _logTag,
        'Error getting config values, returning defaults: $e',
      );
      return _getDefaultValues().map(
        (key, value) => MapEntry(key, value.toString()),
      );
    }
  }

  /// Convenience method to get chat model using current subscription
  String getChatModelForCurrentUser() {
    UserSubscription? currentSubscription;
    try {
      if (SubscriptionService.instance.isInitialized) {
        currentSubscription = SubscriptionService.instance.currentSubscription;
      }
    } catch (e) {
      LoggingService.instance.logInfo(
        _logTag,
        'Could not get current subscription, using free tier: $e',
      );
    }
    return getChatModel(userSubscription: currentSubscription);
  }

  /// Convenience method to get title generation model using current subscription
  String getTitleGenerationModelForCurrentUser() {
    UserSubscription? currentSubscription;
    try {
      if (SubscriptionService.instance.isInitialized) {
        currentSubscription = SubscriptionService.instance.currentSubscription;
      }
    } catch (e) {
      LoggingService.instance.logInfo(
        _logTag,
        'Could not get current subscription, using free tier: $e',
      );
    }
    return getTitleGenerationModel(userSubscription: currentSubscription);
  }

  /// Convenience method to get profile update model using current subscription
  String getProfileUpdateModelForCurrentUser() {
    UserSubscription? currentSubscription;
    try {
      if (SubscriptionService.instance.isInitialized) {
        currentSubscription = SubscriptionService.instance.currentSubscription;
      }
    } catch (e) {
      LoggingService.instance.logInfo(
        _logTag,
        'Could not get current subscription, using free tier: $e',
      );
    }
    return getProfileUpdateModel(userSubscription: currentSubscription);
  }
}
