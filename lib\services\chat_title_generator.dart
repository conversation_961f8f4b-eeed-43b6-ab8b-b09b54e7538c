import 'package:firebase_ai/firebase_ai.dart';
import '../models/models.dart';
import 'remote_config_service.dart';
import 'system_prompt_service.dart';
import 'subscription_service.dart';

class ChatTitleGenerator {
  GenerativeModel? _model;

  ChatTitleGenerator();

  /// Gets or creates the Gemini model instance with configurable model selection
  Future<GenerativeModel> _getModel() async {
    if (_model != null) return _model!;

    final modelName = RemoteConfigService.instance
        .getTitleGenerationModelForCurrentUser();

    _model = FirebaseAI.googleAI().generativeModel(
      model: modelName,
      safetySettings: [
        SafetySetting(
          HarmCategory.dangerousContent,
          HarmBlockThreshold.none,
          null,
        ),
      ],
    );
    return _model!;
  }

  /// Generates a short title (3-10 words) for a chat based on its messages
  Future<String> generateTitle(List<Message> messages) async {
    try {
      // Filter out image messages and get only text content
      final textMessages = messages
          .where((msg) => msg.type == 'text' && msg.textContent != null)
          .map((msg) => msg.textContent!)
          .toList();

      if (textMessages.isEmpty) {
        return 'New Chat';
      }

      // Concatenate all text messages with some context
      final conversationText = textMessages
          .take(10)
          .join('\n'); // Limit to first 10 messages

      // Get current subscription for prompt configuration
      UserSubscription? currentSubscription;
      try {
        if (SubscriptionService.instance.isInitialized) {
          currentSubscription =
              SubscriptionService.instance.currentSubscription;
        }
      } catch (e) {
        // Continue with null subscription if service is not available
      }

      // Get the title generation prompt from SystemPromptService
      final basePrompt = await SystemPromptService.instance
          .getTitleGenerationPrompt(userSubscription: currentSubscription);

      // Create the full prompt with conversation context
      final prompt =
          '''$basePrompt

Conversation:
$conversationText

Generate only the title, nothing else. Do not use quotes or special formatting.''';

      final content = [Content.text(prompt)];
      final model = await _getModel();
      final response = await model.generateContent(content);

      final generatedTitle = response.text?.trim();

      if (generatedTitle != null && generatedTitle.isNotEmpty) {
        // Ensure the title is within reasonable length (max 100 characters)
        return generatedTitle.length > 100
            ? generatedTitle.substring(0, 100).trim()
            : generatedTitle;
      }

      return 'New Chat';
    } catch (e) {
      // If title generation fails, return a default title
      return 'New Chat';
    }
  }
}
